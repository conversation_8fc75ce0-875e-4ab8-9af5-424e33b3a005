import { fireEvent, render, screen, waitFor } from '@gc/utils'
import userEvent from '@testing-library/user-event'
import { noop } from 'es-toolkit'
import React, { useState } from 'react'
import { Controller, FormProvider, useForm } from 'react-hook-form'

import { FileUploadField, FileUploadFieldProps } from './FileUploadField'

// Mock the Loading component
jest.mock('../../loading/Loading', () => ({
  __esModule: true,
  default: ({ type, size }: { type: string; size: string }) => (
    <div data-testid='loading-spinner'>{`${type}-${size}`}</div>
  )
}))

// Mock the SCSS module
jest.mock('./FileUploadField.module.scss', () => ({
  fileUploadField: 'fileUploadField'
}))

// Mock @element components
jest.mock('@element/react-textfield', () => ({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Textfield: (props: any) => (
    <div>
      <input
        data-testid={props['data-testid']}
        placeholder={props.placeholder}
        value={props.value || ''}
        readOnly={props.readOnly}
        required={props.required}
        onChange={props.onChange}
        onBlur={props.onBlur}
        aria-label={props['aria-label']}
        aria-describedby={props['aria-describedby']}
        role={props.role}
        className={props.className}
      />
      {props.helperText && <div data-testid='helper-text'>{props.helperText}</div>}
      {props.trailingIcon && <div data-testid='trailing-icon'>{props.trailingIcon}</div>}
    </div>
  )
}))

jest.mock('@element/react-icon-button', () => ({
  IconButton: ({ icon, onClick, style }: { icon: string; onClick: () => void; style?: React.CSSProperties }) => (
    <button data-testid={`icon-button-${icon}`} onClick={onClick} style={style} type='button'>
      {icon}
    </button>
  )
}))

// Test wrapper component for standalone usage
const StandaloneWrapper = ({ children }: { children: React.ReactNode }) => {
  return <div>{children}</div>
}

// Test wrapper component that provides form context for react-hook-form integration tests
const ReactHookFormWrapper = ({
  children,
  defaultValues = {}
}: {
  children: React.ReactNode
  defaultValues?: Record<string, unknown>
}) => {
  const methods = useForm({ defaultValues })
  return <FormProvider {...methods}>{children}</FormProvider>
}

// Controlled component wrapper for testing
const ControlledWrapper = ({
  children,
  initialValue = null
}: {
  children: (props: any) => React.ReactNode
  initialValue?: File | null
}) => {
  const [value, setValue] = useState<File | null>(initialValue)
  const [error, setError] = useState<string>('')

  return (
    <div>
      {children({ value, onChange: setValue, error })}
      <div data-testid='current-value'>{value?.name || 'No file selected'}</div>
    </div>
  )
}

describe('FileUploadField', () => {
  const defaultProps: Partial<FileUploadFieldProps> = {
    name: 'testFile',
    placeholder: 'Select file',
    helperText: 'Required',
    required: false,
    ariaLabel: 'Upload file'
  }

  const createMockFile = (name: string, size: number, type = 'application/pdf'): File => {
    const file = new File(['test content'], name, { type })
    Object.defineProperty(file, 'size', { value: size })
    return file
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  afterEach(() => {
    jest.restoreAllMocks()
  })

  describe('Standalone Usage', () => {
    it('renders with default props', () => {
      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} />
        </StandaloneWrapper>
      )

      expect(screen.getByTestId('testFile-input')).toBeInTheDocument()
      expect(screen.getByPlaceholderText('Select file')).toBeInTheDocument()
      expect(screen.getByTestId('icon-button-file_upload')).toBeInTheDocument()
    })

    it('renders with custom props', () => {
      const customProps = {
        ...defaultProps,
        placeholder: 'Choose document',
        helperText: 'Upload your document',
        required: true,
        className: 'custom-class',
        ariaLabel: 'Document upload'
      }

      render(
        <StandaloneWrapper>
          <FileUploadField {...customProps} />
        </StandaloneWrapper>
      )

      expect(screen.getByPlaceholderText('Choose document')).toBeInTheDocument()
      expect(screen.getByTestId('testFile-input')).toHaveAttribute('required')
    })

    it('works as controlled component', () => {
      const mockOnChange = jest.fn()
      const testFile = createMockFile('test.pdf', 1024)

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} value={testFile} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      expect(screen.getByDisplayValue('test.pdf')).toBeInTheDocument()
    })
  })

  describe('File Selection', () => {
    it('opens file dialog when upload button is clicked', async () => {
      const user = userEvent.setup()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} />
        </StandaloneWrapper>
      )

      const uploadButton = screen.getByTestId('icon-button-file_upload')
      const fileInput = screen.getByLabelText('Upload file')

      // Mock the click method
      const clickSpy = jest.spyOn(fileInput, 'click').mockImplementation(noop)

      await user.click(uploadButton)

      expect(clickSpy).toHaveBeenCalled()
    })

    it('calls onChange with valid file', async () => {
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const validFile = createMockFile('test.pdf', 1024)

      fireEvent.change(fileInput, { target: { files: [validFile] } })

      // Wait for validation to complete
      await waitFor(
        () => {
          expect(mockOnChange).toHaveBeenCalledWith(validFile)
        },
        { timeout: 1000 }
      )
    })

    it('calls onChange with null for invalid file', async () => {
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const invalidFile = createMockFile('test.exe', 1024, 'application/x-msdownload')

      fireEvent.change(fileInput, { target: { files: [invalidFile] } })

      // Wait for validation to complete
      await waitFor(
        () => {
          expect(mockOnChange).toHaveBeenCalledWith(null)
        },
        { timeout: 1000 }
      )
    })
  })

  describe('React Hook Form Integration', () => {
    it('works with Controller component', async () => {
      const TestForm = () => {
        const { control, watch } = useForm<{ file: File | null }>({
          defaultValues: { file: null }
        })
        const watchedFile = watch('file')

        return (
          <div>
            <Controller
              name='file'
              control={control}
              render={({ field: { onChange, onBlur, value }, fieldState: { error } }) => (
                <FileUploadField
                  {...defaultProps}
                  value={value}
                  onChange={onChange}
                  onBlur={onBlur}
                  error={error?.message}
                />
              )}
            />
            <div data-testid='form-value'>{watchedFile?.name || 'No file'}</div>
          </div>
        )
      }

      render(<TestForm />)

      const fileInput = screen.getByLabelText('Upload file')
      const validFile = createMockFile('test.pdf', 1024)

      fireEvent.change(fileInput, { target: { files: [validFile] } })

      await waitFor(
        () => {
          expect(screen.getByTestId('form-value')).toHaveTextContent('test.pdf')
        },
        { timeout: 1000 }
      )
    })
  })

  describe('File Validation', () => {
    it('validates file extension correctly', async () => {
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const invalidFile = createMockFile('test.txt', 1024, 'text/plain')

      fireEvent.change(fileInput, { target: { files: [invalidFile] } })

      // Wait for validation to complete (300ms timeout in component)
      await waitFor(
        () => {
          expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
          expect(mockOnChange).toHaveBeenCalledWith(null)
        },
        { timeout: 1000 }
      )
    })

    it('validates file size correctly', async () => {
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const largeFile = createMockFile('test.pdf', 6 * 1024 * 1024) // 6MB

      fireEvent.change(fileInput, { target: { files: [largeFile] } })

      await waitFor(
        () => {
          expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
          expect(mockOnChange).toHaveBeenCalledWith(null)
        },
        { timeout: 1000 }
      )
    })

    it('accepts valid file and shows success state', async () => {
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const validFile = createMockFile('test.pdf', 1024)

      fireEvent.change(fileInput, { target: { files: [validFile] } })

      // Wait for validation to complete and show success
      await waitFor(
        () => {
          expect(screen.getByTestId('icon-button-check_circle')).toBeInTheDocument()
          expect(mockOnChange).toHaveBeenCalledWith(validFile)
        },
        { timeout: 1000 }
      )
    })

    it('handles custom file size limit', async () => {
      const customMaxSize = 1024 * 1024 // 1MB
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} maxFileSize={customMaxSize} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const largeFile = createMockFile('test.pdf', 2 * 1024 * 1024) // 2MB

      fireEvent.change(fileInput, { target: { files: [largeFile] } })

      await waitFor(
        () => {
          expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
          expect(mockOnChange).toHaveBeenCalledWith(null)
        },
        { timeout: 1000 }
      )
    })

    it('handles custom allowed extensions', async () => {
      const customExtensions = ['.jpg', '.png']
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} allowedExtensions={customExtensions} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const pdfFile = createMockFile('test.pdf', 1024)

      fireEvent.change(fileInput, { target: { files: [pdfFile] } })

      await waitFor(
        () => {
          expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
          expect(mockOnChange).toHaveBeenCalledWith(null)
        },
        { timeout: 1000 }
      )
    })
  })

  describe('State Management', () => {
    it('resets state when file is cleared', async () => {
      const mockOnChange = jest.fn()
      const testFile = createMockFile('test.pdf', 1024)

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} value={testFile} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      // File name should be displayed
      expect(screen.getByDisplayValue('test.pdf')).toBeInTheDocument()

      // Clear the file
      const fileInput = screen.getByLabelText('Upload file')
      fireEvent.change(fileInput, { target: { files: [] } })

      await waitFor(() => {
        expect(mockOnChange).toHaveBeenCalledWith(null)
        expect(screen.getByTestId('icon-button-file_upload')).toBeInTheDocument()
      })
    })

    it('displays file name when file is selected', async () => {
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const validFile = createMockFile('my-document.pdf', 1024)

      fireEvent.change(fileInput, { target: { files: [validFile] } })

      await waitFor(
        () => {
          expect(screen.getByDisplayValue('my-document.pdf')).toBeInTheDocument()
          expect(mockOnChange).toHaveBeenCalledWith(validFile)
        },
        { timeout: 1000 }
      )
    })

    it('handles external error prop', () => {
      const externalError = 'External validation error'

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} error={externalError} />
        </StandaloneWrapper>
      )

      expect(screen.getByText(externalError)).toBeInTheDocument()
    })
  })

  describe('Status Change Callbacks', () => {
    it('calls onStatusChange callback when status changes', async () => {
      const onStatusChange = jest.fn()
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onStatusChange={onStatusChange} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const validFile = createMockFile('test.pdf', 1024)

      // Initial call with idle status
      expect(onStatusChange).toHaveBeenCalledWith('idle', undefined)

      fireEvent.change(fileInput, { target: { files: [validFile] } })

      // Should be called with loading status
      expect(onStatusChange).toHaveBeenCalledWith('loading', undefined)

      // Wait for validation to complete and success status
      await waitFor(
        () => {
          expect(onStatusChange).toHaveBeenCalledWith('success', undefined)
          expect(mockOnChange).toHaveBeenCalledWith(validFile)
        },
        { timeout: 1000 }
      )
    })

    it('calls onStatusChange with error when validation fails', async () => {
      const onStatusChange = jest.fn()
      const mockOnChange = jest.fn()

      render(
        <StandaloneWrapper>
          <FileUploadField {...defaultProps} onStatusChange={onStatusChange} onChange={mockOnChange} />
        </StandaloneWrapper>
      )

      const fileInput = screen.getByLabelText('Upload file')
      const invalidFile = createMockFile('test.txt', 1024, 'text/plain')

      fireEvent.change(fileInput, { target: { files: [invalidFile] } })

      // Wait for validation to complete and error status
      await waitFor(
        () => {
          expect(onStatusChange).toHaveBeenCalledWith('error', expect.stringContaining('Invalid file type'))
          expect(mockOnChange).toHaveBeenCalledWith(null)
        },
        { timeout: 1000 }
      )
    })
  })

  describe('Accessibility', () => {
    it('renders with ariaDescription for accessibility', () => {
      const propsWithDescription = {
        ...defaultProps,
        ariaDescription: 'Upload a document file for processing'
      }

      render(
        <StandaloneWrapper>
          <FileUploadField {...propsWithDescription} />
        </StandaloneWrapper>
      )

      // Check that the description element is rendered
      expect(screen.getByText('Upload a document file for processing')).toBeInTheDocument()

      // Check that the file input has the correct aria-describedby
      const fileInput = screen.getByLabelText('Upload file')
      expect(fileInput).toHaveAttribute('aria-describedby', 'file-input-description')
    })

  it('validates empty files correctly', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const emptyFile = createMockFile('empty.pdf', 0) // 0 bytes

    fireEvent.change(fileInput, { target: { files: [emptyFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Check that the error message is displayed in helper text
    expect(screen.getByTestId('helper-text')).toHaveTextContent('File is empty. Please select a valid file.')
  })

  it('handles MIME type mismatch validation', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    // Create a file with .pdf extension but wrong MIME type
    const mismatchFile = createMockFile('document.pdf', 1024, 'text/plain')

    fireEvent.change(fileInput, { target: { files: [mismatchFile] } })

    await waitFor(
      () => {
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Check that MIME type error message is displayed
    expect(screen.getByTestId('helper-text')).toHaveTextContent(/File type mismatch/)
  })

  it('handles validation errors gracefully', async () => {
    // Mock console.error to avoid error output in tests
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation(noop)

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    // Create a file that will trigger an error (invalid extension)
    const invalidFile = createMockFile('test.exe', 1024, 'application/x-msdownload')

    fireEvent.change(fileInput, { target: { files: [invalidFile] } })

    await waitFor(
      () => {
        // The component should handle validation errors gracefully and show error state
        expect(screen.getByTestId('icon-button-close')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Check that an error message is displayed
    expect(screen.getByTestId('helper-text')).toHaveTextContent(/Invalid file type/)

    consoleSpy.mockRestore()
  })

  it('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const textInput = screen.getByTestId('testFile-input')

    // Check file input accessibility
    expect(fileInput).toHaveAttribute('aria-label', 'Upload file')
    expect(fileInput).toHaveAttribute('aria-invalid', 'false')

    // Check text input accessibility
    expect(textInput).toHaveAttribute('role', 'button')
    expect(textInput).toHaveAttribute('aria-label', expect.stringContaining('Upload file'))
  })

  it('updates aria-label when file is selected', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const textInput = screen.getByTestId('testFile-input')
    const validFile = createMockFile('my-document.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [validFile] } })

    await waitFor(
      () => {
        expect(textInput).toHaveAttribute('aria-label', expect.stringContaining('Selected file: my-document.pdf'))
      },
      { timeout: 1000 }
    )
  })

  it('clears file input value before opening dialog', async () => {
    const user = userEvent.setup()

    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const uploadButton = screen.getByTestId('icon-button-file_upload')
    const fileInput = screen.getByLabelText('Upload file') as HTMLInputElement

    // Set a value on the file input
    Object.defineProperty(fileInput, 'value', { value: 'test.pdf', writable: true })

    await user.click(uploadButton)

    // File input value should be cleared
    expect(fileInput.value).toBe('')
  })

  it('handles multiple file selection by taking only the first file', async () => {
    render(
      <TestWrapper>
        <FileUploadField {...defaultProps} />
      </TestWrapper>
    )

    const fileInput = screen.getByLabelText('Upload file')
    const file1 = createMockFile('first.pdf', 1024)
    const file2 = createMockFile('second.pdf', 1024)

    fireEvent.change(fileInput, { target: { files: [file1, file2] } })

    await waitFor(
      () => {
        expect(screen.getByDisplayValue('first.pdf')).toBeInTheDocument()
      },
      { timeout: 1000 }
    )

    // Should only show the first file
    expect(screen.queryByDisplayValue('second.pdf')).not.toBeInTheDocument()
  })
})
