/* eslint-disable @nx/enforce-module-boundaries */
import { TestModal } from '@gc/shared/test'
import { BillToParty } from '@gc/types'
import { render, screen } from '@gc/utils'

import ViewThirdPartyFinancingModal from './ViewThirdPartyFinancingModal'

// Mock the custom hook
jest.mock('./hooks/useThirdPartyFinancingData', () => ({
  useThirdPartyFinancingData: jest.fn(() => ({
    requestedTerms: [],
    isLoadingTerms: false,
    isFetchingTerms: false,
    removeTerm: jest.fn(),
    showErrorNotification: jest.fn()
  }))
}))

// Mock the modal hook
jest.mock('@gc/hooks', () => ({
  ...jest.requireActual('@gc/hooks'),
  useModal: jest.fn(() => ({
    openModal: jest.fn()
  })),
  useMemoizedTranslation: jest.fn(() => (key: string, fallback?: string) => fallback || key)
}))

describe('ViewThirdPartyFinancingModal', () => {
  const mockNavigateProps = { handleClose: jest.fn(), icon: 'close' }
  const mockBillToParties: BillToParty[] = [
    {
      name: 'Test Farmer',
      isPrimaryBillTo: true,
      sapAccountId: '123',
      customerNumber: '456'
    }
  ]

  const defaultProps = {
    navigateProps: mockNavigateProps,
    billToParties: mockBillToParties,
    setModalProps: jest.fn()
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should render successfully', () => {
    const { baseElement } = render(<TestModal modalBody={ViewThirdPartyFinancingModal} bodyProps={defaultProps} />)
    expect(baseElement).toBeTruthy()
  })

  it('should call setModalProps with correct structure', () => {
    const setModalProps = jest.fn()
    render(<TestModal modalBody={ViewThirdPartyFinancingModal} bodyProps={{ ...defaultProps, setModalProps }} />)

    expect(setModalProps).toHaveBeenCalledWith(
      expect.objectContaining({
        sectionPadding: 'none',
        headerActions: expect.anything(),
        footerActions: expect.anything()
      })
    )
  })

  it('should display no data message when no terms exist', () => {
    render(<TestModal modalBody={ViewThirdPartyFinancingModal} bodyProps={defaultProps} />)

    expect(screen.getByText('third_party_financing.no_data_header.label')).toBeInTheDocument()
    expect(screen.getByText('third_party_financing.no_data_description.label')).toBeInTheDocument()
  })

  it('should display loading state when data is loading', () => {
    const { useThirdPartyFinancingData } = require('./hooks/useThirdPartyFinancingData')
    useThirdPartyFinancingData.mockReturnValue({
      requestedTerms: [],
      isLoadingTerms: true,
      isFetchingTerms: false,
      removeTerm: jest.fn(),
      showErrorNotification: jest.fn()
    })

    render(<TestModal modalBody={ViewThirdPartyFinancingModal} bodyProps={defaultProps} />)

    // Should show loading skeleton
    expect(document.querySelector('.loading')).toBeTruthy()
  })
})
