/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { Typo<PERSON>aption, TypoOverline, TypoSubtitle } from '@element/react-components'
import { Group } from '@element/react-group'
import { Select } from '@element/react-select'
import { Textfield } from '@element/react-textfield'
import { useMemoizedTranslation, useModal, useSelectedAccount } from '@gc/hooks'
import { BillToParty, Option, ThirdPartyFinancingFormData, ThirdPartyFinancingSchema } from '@gc/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { isUndefined } from 'es-toolkit'
import { memo, useCallback, useEffect, useMemo } from 'react'
import { useForm } from 'react-hook-form'

import { CurrencyField } from '../../../ui-common/form/CurrencyField/CurrencyField'
import { FileUploadField } from '../../../ui-common/form/FileUploadField/FileUploadField'
import TopAppBar from '../../../ui-common/header/TopAppBar'
import ModalActionSlot from '../../../ui-common/modal/ModalActionSlot'
import { LineSkeleton } from '../../../ui-common/skeleton/LineSkeleton'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import { usePaymentTerms } from './hooks/usePaymentTerms'
import { useThirdPartyFinancingData } from './hooks/useThirdPartyFinancingData'
import styles from './ThirdPartyFinancingFormView.module.scss'
import { THIRD_PARTY_FINANCING_CONSTANTS } from './utils/constants'

const { VALIDATION, FORM_IDS } = THIRD_PARTY_FINANCING_CONSTANTS

export interface CreateThirdPartyFinancingModalProps extends ModalDefaultProps {
  billToParties: BillToParty[]
  editingTermId?: string
}

// Modal Header Component
const CreateThirdPartyFinancingModalHeader = memo(
  ({ navigateProps, title }: { navigateProps: { icon: string; handleClose: () => void }; title: string }) => (
    <TopAppBar
      isModalTopBar
      title={title}
      leadingIconButtonProps={{
        icon: 'arrow_back',
        ariaLabel: 'Back',
        onClick: navigateProps.handleClose
      }}
    />
  )
)

// Modal Footer Component
const CreateThirdPartyFinancingModalFooter = memo(
  ({
    onSubmit,
    onCancel,
    isValid,
    isSubmitting
  }: {
    onSubmit: () => void
    onCancel: () => void
    isValid: boolean
    isSubmitting: boolean
  }) => {
    const t = useMemoizedTranslation()

    const submitAction = useMemo(
      () =>
        ({
          onClick: onSubmit,
          disabled: !isValid || isSubmitting,
          loading: isSubmitting,
          label: t('common.submit.label', 'Submit')
        }) as ButtonProps,
      [onSubmit, isValid, isSubmitting, t]
    )

    const cancelAction = useMemo(
      () =>
        ({
          variant: 'outlined',
          onClick: onCancel,
          disabled: isSubmitting,
          label: t('common.cancel.label', 'Cancel')
        }) as ButtonProps,
      [onCancel, isSubmitting, t]
    )

    return <ModalActionSlot primaryAction={submitAction} secondaryAction={cancelAction} />
  }
)

const PaymentTermsSkeleton = memo(function PaymentTermsSkeleton(): JSX.Element {
  return (
    <>
      <LineSkeleton width='60%' />
      <LineSkeleton width='40%' />
    </>
  )
})

export function CreateThirdPartyFinancingModal({
  setModalProps,
  navigateProps,
  billToParties,
  editingTermId
}: CreateThirdPartyFinancingModalProps): JSX.Element {
  const t = useMemoizedTranslation()
  const { closeModal } = useModal()
  const { sapAccountId } = useSelectedAccount()

  // Use the custom hook for data management
  const { selectedFarmer, submitForm, showSuccessNotification, showErrorNotification, selectedPaymentTerm } =
    useThirdPartyFinancingData({ billToParties })

  const { paymentTermsOptions, isPaymentTermsLoading } = usePaymentTerms(selectedFarmer)

  // Form setup
  const defaultValues = useMemo(
    () => ({
      amount: '',
      comments: '',
      memberName: '',
      accountNumber: '',
      userId: sapAccountId || '',
      creditAuthorizationLetter: undefined,
      farmer: selectedFarmer?.name ?? '',
      financingTerm: ''
    }),
    [sapAccountId, selectedFarmer]
  )

  const {
    register,
    setValue,
    watch,
    handleSubmit,
    formState: { errors, isValid }
  } = useForm<ThirdPartyFinancingFormData>({
    defaultValues,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(ThirdPartyFinancingSchema)
  })

  const watchedValues = watch()

  // Farmer options
  const farmerOptions = useMemo(() => {
    return billToParties.map((party) => ({
      id: party.name,
      text: party.name,
      value: party.name
    }))
  }, [billToParties])

  // Handle form submission
  const onSubmit = useCallback(
    async (data: ThirdPartyFinancingFormData) => {
      try {
        await submitForm(data)
        showSuccessNotification(
          t('third_party_financing.submission_success', 'Third-party financing request submitted successfully')
        )
        closeModal()
      } catch (error) {
        console.error('Error submitting form:', error)
        showErrorNotification(
          t('third_party_financing.submission_error', 'Error submitting third-party financing request')
        )
      }
    },
    [submitForm, showSuccessNotification, showErrorNotification, t, closeModal]
  )

  // Handle input changes
  const handleInputChange = useCallback(
    (field: keyof ThirdPartyFinancingFormData) => (event: React.ChangeEvent<HTMLInputElement>) => {
      setValue(field, event.target.value, { shouldValidate: true })
    },
    [setValue]
  )

  // Handle select changes
  const handleSelectChange = useCallback(
    (field: keyof ThirdPartyFinancingFormData) => (option: Option) => {
      setValue(field, option.value, { shouldValidate: true })
    },
    [setValue]
  )

  // Special handler for account number - only allow digits and limit to 10
  const handleAccountNumberChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const inputValue = event.target.value
      const numericValue = inputValue.replace(/\D/g, '').slice(0, VALIDATION.ACCOUNT_NUMBER_LENGTH)
      setValue('accountNumber', numericValue, { shouldValidate: true })
    },
    [setValue]
  )

  // Modal configuration
  const headerActions = useMemo(
    () => (
      <CreateThirdPartyFinancingModalHeader
        navigateProps={navigateProps}
        title={t('third_party_financing.create_form.title', 'Create Third-Party Financing Request')}
      />
    ),
    [navigateProps, t]
  )

  const footerActions = useMemo(
    () => (
      <CreateThirdPartyFinancingModalFooter
        onSubmit={handleSubmit(onSubmit)}
        onCancel={navigateProps.handleClose}
        isValid={isValid}
        isSubmitting={false} // We'll track this in the hook later
      />
    ),
    [handleSubmit, onSubmit, navigateProps.handleClose, isValid]
  )

  const modalProps: ModalPropsType = useMemo(
    () => ({
      sectionPadding: 'none' as const,
      headerActions,
      footerActions
    }),
    [headerActions, footerActions]
  )

  useEffect(() => {
    setModalProps(modalProps)
  }, [setModalProps, modalProps])

  return (
    <div role='form' aria-labelledby='form-title' className={styles.container} id={FORM_IDS.FORM}>
      <div id='form-title' className='sr-only'>
        Third-Party Financing Application Form
      </div>

      {/* Farmer Section */}
      <div className={styles.section}>
        <Select
          {...register('farmer')}
          hoisted
          label={t('third_party_financing_form.farmer.label')}
          variant='outlined'
          value={watchedValues.farmer || ''}
          options={farmerOptions}
          onChange={handleSelectChange('farmer')}
          valid={isUndefined(errors.farmer)}
          helperText={errors.farmer?.message}
          style={{ display: 'flex', alignItems: 'center' }}
        />
      </div>

      {/* Actual Payment Terms Section */}
      <div className={styles.section}>
        <TypoOverline className={styles.sectionTitle}>ACTUAL PAYMENT TERMS</TypoOverline>

        <div role='region' className={styles.paymentTermsCard} aria-labelledby='payment-terms-title'>
          <div id='payment-terms-title' className='sr-only'>
            Current Payment Terms Information
          </div>
          {isPaymentTermsLoading ? (
            <PaymentTermsSkeleton />
          ) : (
            <>
              <TypoSubtitle bold level={2}>
                {selectedPaymentTerm?.title || t('third_party_financing.no_payment_terms.label')}
              </TypoSubtitle>
              {selectedPaymentTerm?.description && <TypoCaption>{selectedPaymentTerm.description}</TypoCaption>}
            </>
          )}
        </div>
      </div>

      {/* Form Fields */}
      <Group fullWidth gap='airy' direction='vertical'>
        {/* Financing Option */}
        <div className={styles.field}>
          <Select
            {...register('financingTerm')}
            hoisted
            required
            variant='outlined'
            label={t('third_party_financing_form.financing_option.label')}
            helperText={errors.financingTerm?.message || 'Required'}
            value={watchedValues.financingTerm || ''}
            options={paymentTermsOptions}
            onChange={handleSelectChange('financingTerm')}
            valid={isUndefined(errors.financingTerm)}
            style={{ display: 'flex', alignItems: 'center' }}
          />
        </div>

        {/* Member Name */}
        <Textfield
          {...register('memberName')}
          fullWidth
          required
          helperTextPersistent
          variant='outlined'
          label={t('third_party_financing_form.member_name.label')}
          placeholder={t('third_party_financing_form.member_name.placeholder')}
          value={watchedValues.memberName ?? ''}
          helperText={errors.memberName?.message || 'Required'}
          onChange={handleInputChange('memberName')}
          valid={isUndefined(errors.memberName)}
        />

        {/* Amount */}
        <CurrencyField
          required
          name='amount'
          label={t('third_party_financing_form.amount.label')}
          placeholder={t('third_party_financing_form.amount.placeholder')}
          helperText={errors.amount?.message || 'Required'}
        />

        {/* Account Number */}
        <Textfield
          {...register('accountNumber')}
          fullWidth
          required
          helperTextPersistent
          variant='outlined'
          label={t('third_party_financing_form.account_number.label')}
          helperText={errors.accountNumber?.message || 'Required'}
          placeholder={t('third_party_financing_form.account_number.placeholder')}
          onChange={handleAccountNumberChange}
          valid={isUndefined(errors.accountNumber)}
          value={watchedValues.accountNumber || ''}
          maxlength={VALIDATION.ACCOUNT_NUMBER_LENGTH}
          inputMode='numeric'
          pattern='[0-9]*'
        />

        {/* Credit Authorization Letter */}
        <FileUploadField
          required
          name='creditAuthorizationLetter'
          ariaLabel='Upload credit authorization letter'
          helperText={t('third_party_financing_form.credit_authorization_letter.helper_text')}
          placeholder={t('third_party_financing_form.credit_authorization_letter.placeholder')}
        />

        {/* Comments */}
        <Textfield
          {...register('comments')}
          fullWidth
          textarea
          variant='outlined'
          placeholder={t('third_party_financing_form.comments.placeholder')}
          value={watchedValues.comments ?? ''}
          onChange={handleInputChange('comments')}
          noResize={false}
        />
      </Group>
    </div>
  )
}

export default CreateThirdPartyFinancingModal
