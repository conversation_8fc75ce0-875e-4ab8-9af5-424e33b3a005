/* eslint-disable @nx/enforce-module-boundaries */
import { useDataSource, useMemoizedTranslation, useModal, useSelectedAccount } from '@gc/hooks'
import {
  setNotification,
  useGetThirdPartyFinancingQuery,
  useGlobalDispatch,
  useRemoveThirdPartyFinancingMutation,
  useSubmitThirdPartyFinancingMutation
} from '@gc/redux-store'
import {
  BillToParty,
  ThirdPartyFinancingFormData,
  ThirdPartyFinancingRequestedTerm,
  ThirdPartyFinancingSchema
} from '@gc/types'
import { zodResolver } from '@hookform/resolvers/zod'
import { createContext, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react'
import { FormProvider, useForm, UseFormReturn } from 'react-hook-form'

import { usePaymentTerms } from '../hooks/usePaymentTerms'
import { createFormData, getErrorMessage } from '../utils'
import { THIRD_PARTY_FINANCING_CONSTANTS } from '../utils/constants'

// Types
export type ModalStep = 'initial' | 'form'

export interface ThirdPartyFinancingError {
  message: string
  code?: string
  field?: string
}

export interface ThirdPartyFinancingState {
  // Modal navigation state
  currentStep: ModalStep
  editingTermId: string | null

  // Data state
  billToParties: BillToParty[]
  requestedTerms: ThirdPartyFinancingRequestedTerm[]
  selectedFarmer: BillToParty | undefined

  // Loading states
  isLoadingTerms: boolean
  isFetchingTerms: boolean
  isSubmitting: boolean

  // Error state
  error: ThirdPartyFinancingError | null

  // Form state
  isDirty: boolean
  isValid: boolean

  // Modal state
  isModalOpen: boolean
}

export interface ThirdPartyFinancingActions {
  // Navigation actions
  navigateToStep: (step: ModalStep) => void
  goBack: () => void
  closeModal: () => void

  // Form actions
  submitForm: (data: ThirdPartyFinancingFormData) => Promise<void>
  resetForm: (withValues?: Partial<ThirdPartyFinancingFormData>) => void

  // Term management actions
  editTerm: (termId: string) => void
  removeTerm: (termId: string) => Promise<void>
  addNewTerm: () => void

  // Error handling
  clearError: () => void
  setError: (error: ThirdPartyFinancingError) => void

  // State management
  refreshTerms: () => void
}

export interface ThirdPartyFinancingContextValue extends ThirdPartyFinancingState {
  actions: ThirdPartyFinancingActions
  methods: UseFormReturn<ThirdPartyFinancingFormData>
}

// Context
const ThirdPartyFinancingContext = createContext<ThirdPartyFinancingContextValue | null>(null)

// Provider Props
export interface ThirdPartyFinancingProviderProps {
  children: React.ReactNode
  initialBillToParties?: BillToParty[]
  initialStep?: ModalStep
  onModalClose?: () => void
}

// Custom hook to use the context
export function useThirdPartyFinancingContext(): ThirdPartyFinancingContextValue {
  const context = useContext(ThirdPartyFinancingContext)
  if (!context) {
    throw new Error('useThirdPartyFinancingContext must be used within a ThirdPartyFinancingProvider')
  }
  return context
}

// Provider Component
export function ThirdPartyFinancingProvider({
  children,
  initialBillToParties = [],
  initialStep = 'initial',
  onModalClose
}: ThirdPartyFinancingProviderProps): JSX.Element {
  // Hooks
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()
  const { closeModal, openModal } = useModal()
  const { sapAccountId } = useSelectedAccount()

  // State
  const billToParties = useRef<BillToParty[]>(initialBillToParties)

  const [currentStep, setCurrentStep] = useState<ModalStep>(initialStep)
  const [editingTermId, setEditingTermId] = useState<string | null>(null)
  const [error, setError] = useState<ThirdPartyFinancingError | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(true)

  // Refs for cleanup
  const cleanupRef = useRef<(() => void) | null>(null)

  // API queries and mutations
  const {
    data: termsData,
    isSuccess: isTermsSuccess,
    isFetching: isFetchingTerms,
    isLoading: isLoadingTerms,
    refetch: refetchTerms
  } = useGetThirdPartyFinancingQuery(sapAccountId)

  const { dataSource: requestedTerms } = useDataSource(termsData ?? [], isTermsSuccess, isFetchingTerms)
  const [submitThirdPartyFinancing, { isLoading: isSubmitting }] = useSubmitThirdPartyFinancingMutation()
  const [removeThirdPartyFinancing] = useRemoveThirdPartyFinancingMutation()

  // Derived state
  const selectedFarmer = useMemo(() => billToParties.current.find((party) => party.isPrimaryBillTo), [billToParties])

  const { paymentTerms } = usePaymentTerms(selectedFarmer)

  // Form setup
  const defaultValues = useMemo(
    () => ({
      amount: '',
      comments: '',
      memberName: '',
      accountNumber: '',
      userId: sapAccountId,
      creditAuthorizationLetter: undefined,
      farmer: selectedFarmer?.name ?? ''
    }),
    [sapAccountId, selectedFarmer]
  )

  const methods = useForm<ThirdPartyFinancingFormData>({
    defaultValues,
    mode: 'onChange',
    reValidateMode: 'onChange',
    resolver: zodResolver(ThirdPartyFinancingSchema)
  })

  const {
    formState: { isDirty, isValid }
  } = methods

  // Error notification helper
  const showErrorNotification = useCallback(
    (errorMessage: string) => {
      dispatch(
        setNotification({
          open: true,
          icon: 'error',
          themeColor: 'danger',
          message: errorMessage
        })
      )
    },
    [dispatch]
  )

  // Success notification helper
  const showSuccessNotification = useCallback(
    (message: string) => {
      dispatch(
        setNotification({
          open: true,
          message
        })
      )
    },
    [dispatch]
  )

  // Actions
  const navigateToStep = useCallback((step: ModalStep) => {
    setCurrentStep(step)
    setError(null) // Clear errors on navigation
  }, [])

  const goBack = useCallback(() => {
    if (currentStep === 'form') {
      setCurrentStep('initial')
      setEditingTermId(null)
    } else {
      // Show abandon modal for confirmation
      openModal({
        name: THIRD_PARTY_FINANCING_CONSTANTS.MODAL_NAMES.ABANDON_THIRD_PARTY_FINANCING
      })
    }
  }, [currentStep, openModal])

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false)
    onModalClose?.()
    closeModal()

    // Cleanup
    if (cleanupRef.current) {
      cleanupRef.current()
    }
  }, [closeModal, onModalClose])

  const submitForm = useCallback(
    async (data: ThirdPartyFinancingFormData): Promise<void> => {
      try {
        setError(null)
        const formData = createFormData(data, paymentTerms)
        const response = await submitThirdPartyFinancing(formData)

        if (response.error) {
          const errorMessage = getErrorMessage(response.error)
          setError({ message: errorMessage })
          showErrorNotification(errorMessage)
        } else {
          showSuccessNotification(THIRD_PARTY_FINANCING_CONSTANTS.MESSAGES.PAYMENT_TERMS_UPDATED)

          // Refresh terms data
          await refetchTerms()

          // Navigate back to initial view
          setCurrentStep('initial')
          setEditingTermId(null)

          // Reset form
          methods.reset(defaultValues)
        }
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        setError({ message: errorMessage })
        showErrorNotification(errorMessage)
      }
    },
    [
      paymentTerms,
      submitThirdPartyFinancing,
      showErrorNotification,
      showSuccessNotification,
      refetchTerms,
      methods,
      defaultValues
    ]
  )

  const resetForm = useCallback(
    (withValues?: Partial<ThirdPartyFinancingFormData>) => {
      methods.reset(withValues ?? defaultValues)
      setError(null)
    },
    [methods, defaultValues]
  )

  const editTerm = useCallback(
    (termId: string) => {
      setEditingTermId(termId)
      navigateToStep('form')
    },
    [navigateToStep]
  )

  const removeTerm = useCallback(
    async (termId: string): Promise<void> => {
      try {
        setError(null)
        const response = await removeThirdPartyFinancing({ sapAccountId, termId })

        if (response.error) {
          const errorMessage = getErrorMessage(response.error)
          setError({ message: errorMessage })
          showErrorNotification(errorMessage)
        } else {
          showSuccessNotification(t('third_party_financing.term_removed.label', 'Request removed'))
          await refetchTerms()
        }
      } catch (error) {
        const errorMessage = getErrorMessage(error)
        setError({ message: errorMessage })
        showErrorNotification(errorMessage)
      }
    },
    [removeThirdPartyFinancing, sapAccountId, showErrorNotification, showSuccessNotification, t, refetchTerms]
  )

  const addNewTerm = useCallback(() => {
    setEditingTermId(null)
    navigateToStep('form')
  }, [navigateToStep])

  const clearError = useCallback(() => {
    setError(null)
  }, [])

  const setErrorAction = useCallback((error: ThirdPartyFinancingError) => {
    setError(error)
  }, [])

  const refreshTerms = useCallback(() => {
    refetchTerms()
  }, [refetchTerms])

  // Reset form when switching to form view for editing
  useEffect(() => {
    if (currentStep === 'form' && editingTermId && requestedTerms) {
      const term = requestedTerms.find((term) => term.id === editingTermId)
      if (term) {
        resetForm(term)
      }
    } else if (currentStep === 'form' && !editingTermId) {
      // Reset for new term
      resetForm()
    }
  }, [currentStep, editingTermId, requestedTerms, resetForm])

  // Cleanup effect
  useEffect(() => {
    cleanupRef.current = () => {
      methods.reset()
      setError(null)
      setEditingTermId(null)
      setCurrentStep('initial')
    }

    return () => {
      if (cleanupRef.current) {
        cleanupRef.current()
      }
    }
  }, [methods])

  // Context value
  const contextValue: ThirdPartyFinancingContextValue = useMemo(
    () => ({
      // State
      error,
      currentStep,
      editingTermId,
      isDirty,
      isFetchingTerms,
      isLoadingTerms,
      isModalOpen,
      isSubmitting,
      isValid,
      requestedTerms,
      selectedFarmer,
      billToParties: billToParties.current,

      // Actions
      actions: {
        navigateToStep,
        goBack,
        closeModal: handleCloseModal,
        submitForm,
        resetForm,
        editTerm,
        removeTerm,
        addNewTerm,
        clearError,
        setError: setErrorAction,
        refreshTerms
      },

      // Form methods
      methods
    }),
    [
      currentStep,
      editingTermId,
      billToParties,
      requestedTerms,
      selectedFarmer,
      isLoadingTerms,
      isFetchingTerms,
      isSubmitting,
      error,
      isDirty,
      isValid,
      isModalOpen,
      navigateToStep,
      goBack,
      handleCloseModal,
      submitForm,
      resetForm,
      editTerm,
      removeTerm,
      addNewTerm,
      clearError,
      setErrorAction,
      refreshTerms,
      methods
    ]
  )

  return (
    <ThirdPartyFinancingContext.Provider value={contextValue}>
      <FormProvider {...methods}>{children}</FormProvider>
    </ThirdPartyFinancingContext.Provider>
  )
}

export default ThirdPartyFinancingProvider
