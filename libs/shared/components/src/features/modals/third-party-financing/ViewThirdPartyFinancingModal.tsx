/* eslint-disable @nx/enforce-module-boundaries */
import { ButtonProps } from '@element/react-button'
import { IconButton } from '@element/react-icon-button'
import { Typo<PERSON>aption, TypoOverline, TypoSubtitle } from '@element/react-typography'
import { useMemoizedTranslation, useModal } from '@gc/hooks'
import { BillToParty, ThirdPartyFinancingRequestedTerm } from '@gc/types'
import { memo, useCallback, useEffect, useMemo } from 'react'

import LoadingAndContingencySection from '../../../sections/contingency/LoadingAndContingencySection'
import TopAppBar from '../../../ui-common/header/TopAppBar'
import List from '../../../ui-common/list/List'
import { ListItemProps } from '../../../ui-common/list/ListItem'
import ModalActionSlot from '../../../ui-common/modal/ModalActionSlot'
import { LineSkeleton } from '../../../ui-common/skeleton/LineSkeleton'
import { ModalDefaultProps, ModalPropsType } from '../modalProps'
import { useThirdPartyFinancingData } from './hooks/useThirdPartyFinancingData'
import styles from './ThirdPartyFinancingView.module.scss'
import { THIRD_PARTY_FINANCING_CONSTANTS } from './utils/constants'

const { FORM_IDS, TEST_IDS } = THIRD_PARTY_FINANCING_CONSTANTS

export interface ViewThirdPartyFinancingModalProps extends ModalDefaultProps {
  billToParties: BillToParty[]
}

// Skeleton component for loading state
const RequestedTermsSkeleton = memo(function RequestedTermsSkeleton(): JSX.Element {
  return (
    <div className={styles.skeletonContainer} role='status' aria-label='Loading requested terms'>
      {Array.from({ length: 3 }).map((_, index) => (
        <div key={index} className={styles.skeletonItem}>
          <LineSkeleton width='70%' />
          <LineSkeleton width='50%' />
          <LineSkeleton width='30%' />
        </div>
      ))}
      <span className='sr-only'>Loading requested financing terms...</span>
    </div>
  )
})

// Modal Header Component
const ViewThirdPartyFinancingModalHeader = memo(
  ({ navigateProps, title }: { navigateProps: { icon: string; handleClose: () => void }; title: string }) => (
    <TopAppBar
      isModalTopBar
      title={title}
      leadingIconButtonProps={{
        icon: 'close',
        ariaLabel: 'Close modal',
        onClick: navigateProps.handleClose
      }}
    />
  )
)

// Modal Footer Component
const ViewThirdPartyFinancingModalFooter = memo(
  ({ onAddNewTerm, onClose }: { onAddNewTerm: () => void; onClose: () => void }) => {
    const t = useMemoizedTranslation()

    const addNewTermAction = useMemo(
      () =>
        ({
          leadingIcon: 'add',
          onClick: onAddNewTerm,
          className: styles.footer_button,
          label: t('common.add_new_term.label', 'Add New Term')
        }) as ButtonProps,
      [onAddNewTerm, t]
    )

    const closeAction = useMemo(
      () =>
        ({
          variant: 'outlined',
          onClick: onClose,
          label: t('common.close.label', 'Close')
        }) as ButtonProps,
      [onClose, t]
    )

    return <ModalActionSlot primaryAction={addNewTermAction} secondaryAction={closeAction} />
  }
)

export function ViewThirdPartyFinancingModal({
  setModalProps,
  navigateProps,
  billToParties
}: ViewThirdPartyFinancingModalProps): JSX.Element {
  const t = useMemoizedTranslation()
  const { openModal } = useModal()

  // Use the custom hook for data management
  const { requestedTerms, isLoadingTerms, isFetchingTerms, removeTerm, showErrorNotification } =
    useThirdPartyFinancingData({ billToParties })

  const primaryBillToParty = useMemo(
    () => billToParties.find((billToParty) => billToParty.isPrimaryBillTo),
    [billToParties]
  )

  const groupedByFarmerTerms = useMemo(() => {
    return requestedTerms.reduce(
      (acc, term) => {
        acc[term.farmer] = acc[term.farmer] || []
        acc[term.farmer].push(term)
        return acc
      },
      {} as Record<string, ThirdPartyFinancingRequestedTerm[]>
    )
  }, [requestedTerms])

  // Handle opening the create modal
  const handleAddNewTerm = useCallback(() => {
    openModal({
      name: 'CREATE_THIRD_PARTY_FINANCING',
      props: {
        billToParties
      }
    })
  }, [openModal, billToParties])

  // Handle term actions (edit/remove)
  const handleTermAction = useCallback(
    (code: string) => {
      // For now, we'll implement edit by opening the create modal with pre-filled data
      // This can be enhanced later to support actual editing
      openModal({
        name: 'CREATE_THIRD_PARTY_FINANCING',
        props: {
          billToParties,
          editingTermId: code
        }
      })
    },
    [openModal, billToParties]
  )

  const handleRemoveTerm = useCallback(
    async (termId: string) => {
      try {
        await removeTerm(termId)
      } catch (error) {
        console.error('Error removing term:', error)
        showErrorNotification('Failed to remove term. Please try again.')
      }
    },
    [removeTerm, showErrorNotification]
  )

  const createTermListItems = useCallback(
    (requestedTerms: ThirdPartyFinancingRequestedTerm[]) => {
      const termItems: ListItemProps[] = []

      for (const term of requestedTerms) {
        termItems.push({
          code: term.id,
          primaryText: (
            <TypoSubtitle bold level={2} id={`term-title-${term.id}`}>
              {term.financingTermDescription}
            </TypoSubtitle>
          ),
          secondaryText: (
            <TypoCaption className={styles.termDetails}>
              Member: {term.memberName} (#: {term.accountNumber})
              <br />
              Amount: ${term.amount}
              <br />
            </TypoCaption>
          ),
          isCustomTrailingBlock: true,
          trailingBlockType: 'icon',
          trailingBlock: (
            <IconButton
              tabIndex={0}
              icon='cancel'
              variant='secondary-on-surface'
              onClick={() => handleRemoveTerm(term.id)}
            />
          )
        })
      }

      return termItems
    },
    [handleRemoveTerm]
  )

  // Loading and data states
  const showLoading = isLoadingTerms || isFetchingTerms
  const hasData = requestedTerms.length > 0

  // Modal configuration
  const headerActions = useMemo(
    () => (
      <ViewThirdPartyFinancingModalHeader
        navigateProps={navigateProps}
        title={t('third_party_financing.requested_payment_terms.label', 'Requested Payment Terms')}
      />
    ),
    [navigateProps, t]
  )

  const footerActions = useMemo(
    () => <ViewThirdPartyFinancingModalFooter onAddNewTerm={handleAddNewTerm} onClose={navigateProps.handleClose} />,
    [handleAddNewTerm, navigateProps.handleClose]
  )

  const modalProps: ModalPropsType = useMemo(
    () => ({
      sectionPadding: 'none' as const,
      headerActions,
      footerActions
    }),
    [headerActions, footerActions]
  )

  useEffect(() => {
    setModalProps(modalProps)
  }, [setModalProps, modalProps])

  return (
    <div role='main' className={styles.container} id={FORM_IDS.INITIAL_PAGE}>
      <div className={styles.contentSection}>
        <LoadingAndContingencySection
          hasError={false}
          hasData={hasData}
          isLoading={showLoading}
          loadingComponent={<RequestedTermsSkeleton />}
          noDataClassName={styles.noDataContainer}
          noDataHeader={t('third_party_financing.no_data_header.label')}
          noDataDescription={t('third_party_financing.no_data_description.label')}
        >
          {Object.entries(groupedByFarmerTerms).map(([farmer, terms]) => (
            <div key={farmer} role='region'>
              <TypoOverline className={styles.farmerName} role='heading' aria-level={2}>
                {farmer} {primaryBillToParty?.name === farmer ? `(${t('common.primary.label')})` : ''}
              </TypoOverline>

              <List
                noPadding
                divider={true}
                items={createTermListItems(terms)}
                onAction={handleTermAction}
                trailingBlockType='icon'
                className={styles.termsList}
                data-testid={TEST_IDS.REQUESTED_TERMS_LIST}
              />
            </div>
          ))}
        </LoadingAndContingencySection>
      </div>
    </div>
  )
}

export default ViewThirdPartyFinancingModal
