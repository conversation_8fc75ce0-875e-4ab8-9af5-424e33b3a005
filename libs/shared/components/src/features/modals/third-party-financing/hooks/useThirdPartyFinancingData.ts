/* eslint-disable @nx/enforce-module-boundaries */
import { useDataSource, useMemoizedTranslation, useSelectedAccount } from '@gc/hooks'
import {
  setNotification,
  useGetThirdPartyFinancingQuery,
  useGlobalDispatch,
  useRemoveThirdPartyFinancingMutation,
  useSubmitThirdPartyFinancingMutation
} from '@gc/redux-store'
import {
  BillToParty,
  SelectedPaymentTerm,
  ThirdPartyFinancingFormData,
  ThirdPartyFinancingRequestedTerm
} from '@gc/types'
import { useCallback, useMemo, useRef } from 'react'

import { createFormData } from '../utils'
import { usePaymentTerms } from './usePaymentTerms'

export interface UseThirdPartyFinancingDataProps {
  billToParties: BillToParty[]
}

export interface UseThirdPartyFinancingDataReturn {
  // Data
  requestedTerms: ThirdPartyFinancingRequestedTerm[]
  billToParties: BillToParty[]
  selectedFarmer: BillToParty | undefined
  selectedPaymentTerm: SelectedPaymentTerm | undefined

  // Loading states
  isLoadingTerms: boolean
  isFetchingTerms: boolean
  isSubmitting: boolean
  isRemoving: boolean

  // Actions
  submitForm: (data: ThirdPartyFinancingFormData) => Promise<void>
  removeTerm: (termId: string) => Promise<void>
  refreshTerms: () => void

  // Notifications
  showSuccessNotification: (message: string) => void
  showErrorNotification: (message: string) => void
}

/**
 * Custom hook for managing third-party financing data and operations.
 * This hook provides a centralized way to manage data sharing between
 * the view and create modals, replacing the context-based approach.
 */
export function useThirdPartyFinancingData({
  billToParties
}: UseThirdPartyFinancingDataProps): UseThirdPartyFinancingDataReturn {
  const t = useMemoizedTranslation()
  const dispatch = useGlobalDispatch()
  const { sapAccountId } = useSelectedAccount()

  // Store bill-to-parties in a ref to maintain reference stability
  const billToPartiesRef = useRef<BillToParty[]>(billToParties)
  billToPartiesRef.current = billToParties

  // Derived data
  const selectedFarmer = useMemo(() => billToPartiesRef.current.find((party) => party.isPrimaryBillTo), [])
  const { paymentTerms, selectedPaymentTerm } = usePaymentTerms(selectedFarmer)

  // API hooks
  const {
    data: termsData,
    isLoading: isLoadingTerms,
    isFetching: isFetchingTerms,
    isSuccess: isTermsSuccess,
    refetch: refetchTerms
  } = useGetThirdPartyFinancingQuery(sapAccountId, {
    skip: !sapAccountId
  })

  const [submitThirdPartyFinancing, { isLoading: isSubmitting }] = useSubmitThirdPartyFinancingMutation()
  const [removeThirdPartyFinancing, { isLoading: isRemoving }] = useRemoveThirdPartyFinancingMutation()

  // Process terms data
  const { dataSource: requestedTerms } = useDataSource(termsData ?? [], isTermsSuccess, isFetchingTerms)

  // Notification helpers
  const showErrorNotification = useCallback(
    (errorMessage: string) => {
      dispatch(
        setNotification({
          open: true,
          icon: 'error',
          themeColor: 'danger',
          message: errorMessage
        })
      )
    },
    [dispatch]
  )

  const showSuccessNotification = useCallback(
    (message: string) => {
      dispatch(
        setNotification({
          open: true,
          message
        })
      )
    },
    [dispatch]
  )

  // Form submission handler
  const submitForm = useCallback(
    async (data: ThirdPartyFinancingFormData) => {
      try {
        const formData = createFormData(data, paymentTerms)
        await submitThirdPartyFinancing(formData)

        showSuccessNotification(
          t('third_party_financing.submission_success', 'Third-party financing request submitted successfully')
        )

        // Refresh the terms data
        refetchTerms()
      } catch (error) {
        console.error('Error submitting third-party financing:', error)
        showErrorNotification(
          t('third_party_financing.submission_error', 'Error submitting third-party financing request')
        )
        throw error
      }
    },
    [paymentTerms, submitThirdPartyFinancing, showSuccessNotification, showErrorNotification, t, refetchTerms]
  )

  // Term removal handler
  const removeTerm = useCallback(
    async (termId: string) => {
      try {
        await removeThirdPartyFinancing({ sapAccountId, termId }).unwrap()

        showSuccessNotification(
          t('third_party_financing.removal_success', 'Third-party financing term removed successfully')
        )

        // Refresh the terms data
        refetchTerms()
      } catch (error) {
        console.error('Error removing third-party financing term:', error)
        showErrorNotification(t('third_party_financing.removal_error', 'Error removing third-party financing term'))
        throw error
      }
    },
    [removeThirdPartyFinancing, sapAccountId, showSuccessNotification, t, refetchTerms, showErrorNotification]
  )

  // Refresh terms data
  const refreshTerms = useCallback(() => {
    refetchTerms()
  }, [refetchTerms])

  return {
    // Data
    requestedTerms,
    billToParties: billToPartiesRef.current,
    selectedFarmer,
    selectedPaymentTerm,

    // Loading states
    isLoadingTerms,
    isFetchingTerms,
    isSubmitting,
    isRemoving,

    // Actions
    submitForm,
    removeTerm,
    refreshTerms,

    // Notifications
    showSuccessNotification,
    showErrorNotification
  } as const
}
